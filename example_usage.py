#!/usr/bin/env python3
"""
Zeitwahl AI Agent - Example Usage

This script demonstrates how to use the Zeitwahl AI Agent system
in different scenarios and configurations.
"""

import asyncio
import logging
import os
from datetime import datetime

from app.utils.event_bus import event_bus
from app.utils.events import MessageReceived, ResponseReady
from app.preprocess import Preprocessor
from app.services import LLMService, CalendarService, UserService
from app.postprocess import Postprocessor


class ExampleUsage:
    """Example usage scenarios for the Zeitwahl AI Agent."""
    
    def __init__(self):
        self.setup_logging()
        self.preprocessor = None
        self.llm_service = None
        self.calendar_service = None
        self.user_service = None
        self.postprocessor = None
        self.responses = []
    
    def setup_logging(self):
        """Setup logging for the example."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        # Reduce noise from libraries
        logging.getLogger('aiogram').setLevel(logging.WARNING)
    
    async def initialize_components(self):
        """Initialize all system components."""
        print("🔧 Initializing Zeitwahl AI Agent components...")
        
        # Initialize services
        self.user_service = UserService()
        self.calendar_service = CalendarService()
        
        # Initialize processing pipeline
        self.preprocessor = Preprocessor()
        self.llm_service = LLMService()
        self.postprocessor = Postprocessor()
        
        # Subscribe to ResponseReady events to capture responses
        await event_bus.subscribe_tagged_methods(self)
        
        print("✅ All components initialized successfully!")
    
    @event_bus.subscribe(ResponseReady)
    async def capture_response(self, event: ResponseReady):
        """Capture responses for demonstration purposes."""
        self.responses.append({
            "user_id": event.user_id,
            "response": event.response_text,
            "type": event.response_type
        })
        print(f"📤 Response: {event.response_text}")
    
    async def simulate_user_message(self, user_id: int, chat_id: int, message_text: str):
        """Simulate a user sending a message."""
        print(f"📥 User {user_id}: {message_text}")
        
        # Create MessageReceived event
        message_event = MessageReceived(
            user_id=user_id,
            chat_id=chat_id,
            message_id=len(self.responses) + 1,
            message_text=message_text,
            username="demo_user",
            first_name="Demo",
            last_name="User"
        )
        
        # Publish the event to trigger processing
        await event_bus.publish(message_event)
        
        # Wait for processing to complete
        await asyncio.sleep(1.0)
    
    async def demo_basic_conversation(self):
        """Demonstrate basic conversation flow."""
        print("\n🎯 Demo: Basic Conversation Flow")
        print("=" * 50)
        
        user_id = 12345
        chat_id = 67890
        
        # Simulate various user messages
        messages = [
            "Hello! Can you help me with my calendar?",
            "I need to schedule a meeting tomorrow at 2 PM",
            "What's on my calendar today?",
            "Find me a free hour this week for a team meeting",
            "Cancel my 3 PM meeting today"
        ]
        
        for message in messages:
            await self.simulate_user_message(user_id, chat_id, message)
            print()  # Add spacing between messages
    
    async def demo_calendar_operations(self):
        """Demonstrate calendar operations."""
        print("\n📅 Demo: Calendar Operations")
        print("=" * 50)
        
        # Test calendar service directly
        print("Creating a test event...")
        event_data = {
            "title": "Demo Meeting",
            "start_time": "2024-01-15T14:00:00Z",
            "end_time": "2024-01-15T15:00:00Z",
            "description": "A demonstration meeting",
            "attendees": ["<EMAIL>"]
        }
        
        created_event = await self.calendar_service.create_event("mock", event_data)
        print(f"✅ Created event: {created_event['title']} (ID: {created_event['id']})")
        
        # Find available slots
        print("\nFinding available time slots...")
        available_slots = await self.calendar_service.find_available_slots(
            "mock", 60, "2024-01-15T09:00:00Z", "2024-01-15T17:00:00Z"
        )
        print(f"✅ Found {len(available_slots)} available slots")
        for i, slot in enumerate(available_slots[:3]):  # Show first 3
            print(f"   {i+1}. {slot['start_time']} - {slot['end_time']}")
        
        # Check for conflicts
        print("\nChecking for conflicts...")
        conflicts = await self.calendar_service.check_conflicts(
            "mock", "2024-01-15T14:30:00Z", "2024-01-15T15:30:00Z"
        )
        if conflicts:
            print(f"⚠️  Found {len(conflicts)} conflicts:")
            for conflict in conflicts:
                print(f"   - {conflict['title']} ({conflict['start_time']} - {conflict['end_time']})")
        else:
            print("✅ No conflicts found")
    
    async def demo_user_management(self):
        """Demonstrate user management features."""
        print("\n👤 Demo: User Management")
        print("=" * 50)
        
        user_id = 12345
        
        # Create a user
        user_data = await self.user_service.create_user(
            user_id, "demo_user", "Demo", "User"
        )
        print(f"✅ Created user: {user_data['first_name']} {user_data['last_name']}")
        
        # Start a session
        session = await self.user_service.start_session(user_id)
        print(f"✅ Started session: {session['session_id']}")
        
        # Simulate some activity
        await self.user_service.update_session_activity(user_id, "message")
        await self.user_service.update_session_activity(user_id, "event_created")
        await self.user_service.update_session_activity(user_id, "query")
        
        # End session
        ended_session = await self.user_service.end_session(user_id)
        print(f"✅ Ended session, duration: {ended_session['duration']:.2f}s")
        
        # Get analytics
        analytics = await self.user_service.get_user_analytics(user_id)
        print(f"📊 User analytics:")
        print(f"   - Total messages: {analytics['total_messages']}")
        print(f"   - Total events created: {analytics['total_events_created']}")
        print(f"   - Session count: {analytics['session_count']}")
        print(f"   - Engagement level: {analytics['engagement_level']}")
    
    async def demo_error_handling(self):
        """Demonstrate error handling scenarios."""
        print("\n🚨 Demo: Error Handling")
        print("=" * 50)
        
        user_id = 99999
        chat_id = 88888
        
        # Test various error scenarios
        error_scenarios = [
            "",  # Empty message
            "x" * 5000,  # Too long message
            "<script>alert('test')</script>",  # Potentially malicious content
        ]
        
        for i, scenario in enumerate(error_scenarios):
            print(f"\nTesting error scenario {i+1}...")
            await self.simulate_user_message(user_id, chat_id, scenario)
    
    async def demo_event_bus(self):
        """Demonstrate event bus functionality."""
        print("\n🚌 Demo: Event Bus")
        print("=" * 50)
        
        events_received = []
        
        class DemoEventHandler:
            @event_bus.subscribe("DemoEvent")
            async def handle_demo_event(self, event):
                events_received.append(event)
                print(f"📨 Received demo event: {event}")
        
        # Subscribe handler
        handler = DemoEventHandler()
        await event_bus.subscribe_tagged_methods(handler)
        
        # Publish some events
        await event_bus.publish("DemoEvent", "Hello from event bus!")
        await event_bus.publish("DemoEvent", "Another test event")
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        print(f"✅ Processed {len(events_received)} events through event bus")
    
    async def run_all_demos(self):
        """Run all demonstration scenarios."""
        print("🚀 Starting Zeitwahl AI Agent Demonstration")
        print("=" * 60)
        
        await self.initialize_components()
        
        # Run all demos
        await self.demo_basic_conversation()
        await self.demo_calendar_operations()
        await self.demo_user_management()
        await self.demo_error_handling()
        await self.demo_event_bus()
        
        print("\n🎉 Demonstration completed!")
        print(f"📊 Total responses generated: {len(self.responses)}")
        
        # Clean up
        await event_bus.clear_subscribers()


async def main():
    """Main function to run the example usage."""
    # Set environment variables for demo (if not already set)
    if not os.getenv("TELEGRAM_BOT_TOKEN"):
        os.environ["TELEGRAM_BOT_TOKEN"] = "demo_token_not_for_real_use"
    
    # Create and run the example
    example = ExampleUsage()
    await example.run_all_demos()


if __name__ == "__main__":
    print("Zeitwahl AI Agent - Example Usage")
    print("This script demonstrates the system without requiring a real Telegram bot token.")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
