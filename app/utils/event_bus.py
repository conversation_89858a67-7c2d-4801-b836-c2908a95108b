import asyncio
import logging
import traceback
import typing
from collections import defaultdict, deque
from functools import wraps
from typing import Any, Callable, Deque, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from abc import ABC
import bisect

logger = logging.getLogger(__name__)

@dataclass
class BaseEvent(ABC):
    """Base class for all events in the system."""
    pass

class EventBus:
    """
    Optimized event bus for high-performance event-driven communication.
    Handles 500+ events/sec with minimal latency and memory footprint.
    """

    def __init__(self, max_registry_size: int = 1000):
        self.listeners: Dict[str, List[Tuple[Callable, int]]] = defaultdict(list)
        self.registry: Deque[Dict[str, Any]] = deque(maxlen=max_registry_size)
        self._lock = asyncio.Lock()
        logger.info(f"EventBus initialized with registry size {max_registry_size}")

    def _get_topic_name(self, event_type: Union[str, type]) -> str:
        return event_type.__name__ if isinstance(event_type, type) else str(event_type)

    def subscribe(self, topic: Union[str, type], priority: int = 0) -> Callable:
        """Decorator for subscription with priority support."""
        topic_str = self._get_topic_name(topic)
        
        def decorator(func: Callable) -> Callable:
            func.__event_bus_subscription__ = (topic_str, priority) # type: ignore
            return func
            
        return decorator

    async def subscribe_to_topic(
        self, 
        event_type: Union[str, type], 
        callback: Callable, 
        priority: int = 0
    ) -> None:
        """Thread-safe subscription with priority ordering."""
        topic_str = self._get_topic_name(event_type)
        
        async with self._lock:
            # Insert sorted by priority (higher first)
            new_item = (callback, priority)
            bisect.insort(
                self.listeners[topic_str], 
                new_item, 
                key=lambda x: -x[1]  # Negative for descending order
            )
            
            # Bounded registry update
            self.registry.append({
                "event_type": topic_str,
                "action": "subscribe",
                "timestamp": asyncio.get_event_loop().time(),
                "callback": callback.__qualname__
            })
        
        logger.debug(f"Subscribed {callback.__qualname__} to {topic_str}")

    async def subscribe_tagged_methods(self, obj: object) -> None:
        """Batch subscribe tagged methods with thread safety."""
        for name in dir(obj):
            method = getattr(obj, name)
            if hasattr(method, "__event_bus_subscription__"):
                topic_str, priority = method.__event_bus_subscription__
                await self.subscribe_to_topic(topic_str, method, priority)

    async def publish(
        self, 
        event_type: Union[str, type, BaseEvent], 
        message: Any = None
    ) -> None:
        """
        Non-blocking publish with fire-and-forget semantics.
        Handles 500+ events/sec with minimal publisher latency.
        """
        # Resolve event type and payload
        if isinstance(event_type, BaseEvent):
            topic_str = type(event_type).__name__
            event_data = event_type
        else:
            topic_str = self._get_topic_name(event_type)
            event_data = message

        # Fast path with lock-free listener check
        if topic_str not in self.listeners:
            return

        async with self._lock:
            # Copy handlers to prevent lock contention
            handlers = self.listeners[topic_str][:]
            self.registry.append({
                "event_type": topic_str,
                "action": "publish",
                "timestamp": asyncio.get_event_loop().time(),
                "data": str(event_data)[:100]  # Truncated for memory
            })

        # Fire handlers without waiting
        for callback, _ in handlers:
            asyncio.create_task(self._safe_execute(callback, event_data))

    async def send(
        self, 
        event_type: Union[str, type], 
        message: Any = None
    ) -> Optional[Any]:
        """Direct RPC-style call to highest priority handler."""
        topic_str = self._get_topic_name(event_type)
        
        async with self._lock:
            if not self.listeners.get(topic_str):
                return None
            callback, _ = self.listeners[topic_str][0]  # Highest priority

        # Direct execution for minimal latency
        return await self._execute_handler(callback, message)

    async def _safe_execute(self, callback: Callable, message: Any) -> None:
        """Fire-and-forget handler with error isolation."""
        try:
            await self._execute_handler(callback, message)
        except Exception as e:
            logger.error(f"Handler error: {callback.__name__}: {e}")

    async def _execute_handler(self, callback: Callable, message: Any) -> Any:
        """Universal handler execution with async support."""
        return (
            await callback(message) 
            if asyncio.iscoroutinefunction(callback) 
            else callback(message)
        )

    async def clear_subscribers(self) -> None:
        """Thread-safe reset of all subscriptions."""
        async with self._lock:
            self.listeners.clear()
        logger.info("Cleared all subscribers")

# Global optimized instance
event_bus = EventBus()